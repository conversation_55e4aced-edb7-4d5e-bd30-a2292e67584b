<template>
	<view class="page">
		<view class="header">
			<view class="header_item" v-for="(item, index) in list" :key="index" @click="handleHeader(item)">
				<view :style="currentIndex == item.value ? 'color:#2E80FE;' : ''">{{ item.name }}</view>
				<view class="blue" :style="currentIndex == item.value ? '' : 'background-color:#fff;'"></view>
			</view>
		</view>
		<view @click="dingyue()" class="main">
			<!-- 主订单 -->
			<view class="main_item" v-for="(item, index) in orderList" :key="index" @click="goDetail(item)">
				<view class="head">
					<view class="no">单号：{{ item.orderCode }}</view>
					<view class="type">{{ item.payType == -1 ? '已取消' : pay_typeArr[item.payType] }}</view>
				</view>
				<view class="mid">
					<view class="lef">
						<image :src="item.goodsCover" mode=""></image>
						<text>{{ item.goodsName }}</text>
					</view>
					<view class="righ" v-if="item.payType == 7 || (item.payType == 7 && item.isAftermarket === 1)">
						<view>￥{{ item.coachServicePrice }}</view>
						<view>x{{ item.num }}</view>
					</view>
				</view>
				<view class="bot">
					<view class="qzf" v-if="item.payType === 3 ||item.payType === 5 " @click.stop="showDiffApplyModal(item)">
					 差价申请
					</view>
					
					<view class="qzf" v-if="item.payType >= -1 && item.payType < 7" @click.stop="cancellModal(item)">
						  取消接单
						</view>
					<view class="qzf" v-if="item.payType === 3" @click.stop="showConfirmModal(item, 'queren')">
						确认到达
					</view>
					<view class="qzf" v-if="item.payType === 5" @click.stop="showConfirmModal(item, 'startFu')">
						开始服务
					</view>
				</view>
				
				<!-- 子订单（差价申请列表） -->
				<view v-if="item.orderDiffPriceList && item.orderDiffPriceList.length > 0" class="sub_orders">
					<view class="sub_title">差价申请记录</view>
					<view class="sub_item" v-for="(diffItem, diffIndex) in item.orderDiffPriceList" :key="diffItem.id" @click.stop="">
						<view class="sub_head">
							<view class="sub_no">差价单号：{{ diffItem.diffCode }}</view>
							<view class="sub_status">{{ getDiffStatusText(diffItem.status) }}</view>
						</view>
						<view class="sub_content">
							<view class="sub_info">
								<view class="sub_amount">差价金额：￥{{ diffItem.diffAmount }}</view>
								<view class="sub_reason">原因：{{ diffItem.reasonDetail }}</view>
								<view class="sub_time">申请时间：{{ diffItem.createdTime }}</view>
							</view>
							<view class="sub_actions">
								<view class="sub_qzf" v-if="diffItem.status === 0" @click.stop="showDiffCancelModal(item, diffItem)">
									取消差价
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<u-loadmore :status="status" @loadmore="loadMore" />

		<!-- 自定义差价申请弹窗 -->
		<view class="diff-apply-modal" v-if="showDiffApply" @click="closeDiffApplyModal">
			<view class="modal-content" @click.stop="">
				<!-- 弹窗头部 -->
				<view class="modal-header">
					<view class="modal-title">差价申请</view>
					<view class="close-btn" @click="closeDiffApplyModal">
						<text class="close-icon">×</text>
					</view>
				</view>

				<!-- 弹窗内容 -->
				<view class="modal-body">
					<u--form
						labelPosition="left"
						:model="diffApplyForm"
						:rules="diffApplyRules"
						ref="diffApplyForm"
					>
						<u-form-item label="差价金额" prop="diffAmount" borderBottom ref="item1">
							<u--input v-model="diffApplyForm.diffAmount" placeholder="请输入差价金额" type="number" border="none"></u--input>
						</u-form-item>
						<u-form-item label="原因类型" borderBottom>
							<view class="reason-type-display">
								<text class="reason-type-text">配件不符合</text>
								<view class="reason-type-badge">默认</view>
							</view>
						</u-form-item>
						<u-form-item label="原因详情" prop="reasonDetail" borderBottom ref="item3">
							<u--textarea v-model="diffApplyForm.reasonDetail" placeholder="请输入差价原因详情" count></u--textarea>
						</u-form-item>
						<u-form-item label="配件图片" borderBottom ref="item4">
							<view class="upload-container">
								<upload @upload="imgUploadDiff" @del="imgUploadDiff" :imagelist="diffApplyForm.partsimgs"
									imgtype="partsimgs" imgclass="parts-img" text="上传配件图片" :imgsize="9"></upload>
							</view>
						</u-form-item>
					</u--form>
				</view>

				<!-- 弹窗底部按钮 -->
				<view class="modal-footer">
					<view class="btn-cancel" @click="closeDiffApplyModal">取消</view>
					<view class="btn-confirm" @click="diffApplyConfirm">确认申请</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import Upload from '@/components/upload.vue'; // Import upload component
	export default {
		components: {
			Upload,
		},
		data() {
			return {
				limit: 10,
				coachId: '',
				shifuId: '',
				tmplIds: [
					'qt5Q70oqYNTB2RaTzl0peEHpjK-iinWr3ZfxGvs4wtg',
					'DxiqXzK4yxCTYAqmeK9lEsU0A5-XCF9Fy7kSyX2vmnk',
					'_2z7Bbw8oq4av-yqP31fZLaI82Z_N52wNM_1ihXQv6I',
					'iD-jH6RYVcTr-KDBlH8w7ZTQOSEPeXh02Z9pkvWq5JY'
				],
				status: 'loadmore',
				list: [{
						name: '全部',
						value: 0
					},
					{
						name: '待上门',
						value: 3
					},
					{
						name: '待服务',
						value: 5
					},
					{
						name: '服务中',
						value: 6
					},
					{
						name: '已完成',
						value: 7
					},
					{
						name: '售后',
						value: 8
					},
				],
				currentIndex: 0,
				page: 0,
				orderList: [],
				pay_typeArr: ['', '待支付', '已支付', '已接单', '上门中', '待服务', '服务中', '已完成', '售后'],
				isLoading: false, // Flag to prevent multiple API calls

				// For diffapply modal
				showDiffApply: false,
				currentOrderItemForDiff: null, // To store the item for which diff apply is initiated
				diffApplyForm: {
					diffAmount: '',
					reasonType: 1, // 差价原因类型，目前固定为1代表配件不符合
					reasonDetail: '',
					partsimgs: [], // 配件图片
				},
				diffApplyRules: {
					diffAmount: [{
						required: true,
						message: '请输入差价金额',
						trigger: ['blur', 'change']
					}, {
						validator: (rule, value, callback) => {
							return value >= 0.01;
						},
						message: '差价金额必须大于等于0.01',
						trigger: ['blur', 'change']
					}],
					reasonDetail: [{
						required: true,
						message: '请输入差价原因详情',
						trigger: ['blur', 'change']
					}],
				}
			};
		},
		onReady() {
			// We need to call this in onReady to ensure u-form is ready
			this.$refs.diffApplyForm.setRules(this.diffApplyRules);
		},
		onReachBottom() {
			this.loadMore();
		},
		onPullDownRefresh() {
			this.refreshList();
		},
		methods: {
			// 获取差价申请状态文本
			getDiffStatusText(status) {
				const statusMap = {
					'-1': '已取消',
					0: '待确认',
					1: '已确认待支付', 
					2: '已支付',
					3: '已拒绝'
				};
				return statusMap[status] || '未知状态';
			},
			
			showDiffApplyModal(item) {
				this.currentOrderItemForDiff = item;
				this.diffApplyForm = { // Reset form for new application
					diffAmount: '',
					reasonType: 1, // 差价原因类型，目前固定为1代表配件不符合
					reasonDetail: '',
					partsimgs: [], // 配件图片
				};
				this.showDiffApply = true;
			},

			// 处理差价申请中的图片上传
			imgUploadDiff(e) {
				console.log('imgUploadDiff event:', e);
				const { imagelist, imgtype } = e;
				this.$set(this.diffApplyForm, imgtype, imagelist);
			},

			// 关闭差价申请弹窗
			closeDiffApplyModal() {
				this.showDiffApply = false;
				// 重置表单数据
				this.diffApplyForm = {
					diffAmount: '',
					reasonType: 1,
					reasonDetail: '',
					partsimgs: [],
				};
			},

			async diffApplyConfirm() {
				// Validate the form before submitting
				try {
					await this.$refs.diffApplyForm.validate();
					// Form is valid, proceed with API call
					if (!this.currentOrderItemForDiff) {
						uni.showToast({
							icon: 'none',
							title: '订单信息缺失'
						});
						return;
					}

					try {
						// 准备配件图片数据
						const partsImgPaths = this.diffApplyForm.partsimgs.map(img => img.path);

						const res = await this.$api.shifu.diffApply({
							orderId: this.currentOrderItemForDiff.id,
							diffAmount: parseFloat(this.diffApplyForm.diffAmount),
							reasonType: this.diffApplyForm.reasonType, // 差价原因类型
							reasonDetail: this.diffApplyForm.reasonDetail,
							partsimgs: partsImgPaths // 配件图片路径数组
						});
						if (res.code === "200") {
							uni.showToast({
								title: '差价申请成功',
								icon: 'success'
							});
							this.showDiffApply = false;
							this.refreshList(); // Refresh list to reflect changes
						} else {
							uni.showToast({
								title: res.msg || '差价申请失败',
								icon: 'none'
							});
						}
					} catch (err) {
						uni.showToast({
							title: '请求失败',
							icon: 'none'
						});
						console.error('Error in diffApply:', err);
					}
				} catch (errors) {
					console.error('Form validation failed:', errors);
					uni.showToast({
						icon: 'none',
						title: '请检查填写信息'
					});
				}
			},

			showDiffCancelModal(item, diffItem ) {
				const title = diffItem ? '取消差价申请' : '取消差价申请';
				const content = diffItem ? 
					`确定要取消差价单号 ${diffItem.diffCode} 的申请吗？` : 
					'确定要取消此订单的差价申请吗？';
				
				uni.showModal({
					title: title,
					content: content,
					confirmText: '确定',
					cancelText: '取消',
					success: (res) => {
						if (res.confirm) {
							this.diffCancel(item, diffItem);
						}
					}
				});
			},

			async diffCancel(item, diffItem) {
				try {
					console.log(diffItem)
					// 如果有具体的差价申请项，使用差价申请的ID，否则使用订单ID
					const cancelId = diffItem.id 
					const res = await this.$api.shifu.diffCancel({
						id:cancelId
					});
					if (res.code === "200") {
						uni.showToast({
							title: '差价申请已取消',
							icon: 'success'
						});
						this.refreshList();
					} else {
						uni.showToast({
							title: res.msg || '取消失败',
							icon: 'none'
						});
					}
				} catch (err) {
					uni.showToast({
						title: '请求失败',
						icon: 'none'
					});
					console.error('Error in diffCancel:', err);
				}
			},

			cancellModal(item) {
				uni.showModal({
					title: '取消订单',
					content: '确定要取消此订单吗？',
					confirmText: '确定',
					cancelText: '取消',
					success: (res) => {
						if (res.confirm) {
							this.$api.shifu.orderCancel({
								id: item.id
							}).then(res => {
								if (res.code === "200") { // Assuming "200" is success code
									uni.showToast({
										icon: 'success',
										title: res.msg || '订单已取消'
									});
									this.getList();
								} else {
									uni.showToast({
										icon: 'none',
										title: res.msg || '取消失败'
									});
								}
							}).catch(err => {
								uni.showToast({
									icon: 'none',
									title: '请求失败'
								});
								console.error('Error cancelling order:', err);
							});
						}
					}
				});
			},
			
			dingyue() {
				console.log('dingyue called');
				const allTmplIds = this.tmplIds;
				const requiredTmplId = 'DxiqXzK4yxCTYAqmeK9lEsU0A5-XCF9Fy7kSyX2vmnk';
				if (allTmplIds.length < 3) {
					console.error("Not enough template IDs available:", allTmplIds);
					return;
				}
				// Ensure requiredTmplId is included, select 2 more randomly
				const otherTmplIds = allTmplIds.filter(id => id !== requiredTmplId);
				const shuffled = otherTmplIds.sort(() => 0.5 - Math.random());
				const selectedTmplIds = [requiredTmplId, ...shuffled.slice(0, 2)];
				console.log("Selected template IDs:", selectedTmplIds);
				const templateData = selectedTmplIds.map((id, index) => ({
					templateId: id,
					templateCategoryId: index === 0 ? 10 : 5
				}));
				uni.requestSubscribeMessage({
					tmplIds: selectedTmplIds,
					success: (res) => {
						console.log('requestSubscribeMessage result:', res);
						this.templateCategoryIds = [];
						let count = 0;
						selectedTmplIds.forEach((templId, index) => {
							console.log(`Template ${templId} status: ${res[templId]}`);
							if (res[templId] === 'accept') {
								const templateCategoryId = templateData[index].templateCategoryId;
								if (templateCategoryId === 10) {
									for (let i = 0; i < 15; i++) {
										this.templateCategoryIds.push(templateCategoryId);
									}
								}
								// Do not push if templateCategoryId is 5
								// else {
								// 	this.templateCategoryIds.push(templateCategoryId);
								// }
								console.log('Accepted message push for template:', templId);
							}
						});
						console.log('Updated templateCategoryIds:', this.templateCategoryIds);
					},
					fail: (err) => {
						console.error('requestSubscribeMessage failed:', err);
					}
				});
			},
			
			loadMore() {
				if (this.status === 'nomore' || this.isLoading) return;
				this.isLoading = true;
				this.status = 'loading';

				const nextPage = this.page + 1;
				this.fetchOrders(nextPage, false);
			},

			refreshList() {
				this.page = 0;
				this.orderList = [];
				this.status = 'loadmore';
				this.getList();
				setTimeout(() => {
					uni.stopPullDownRefresh();
				}, 1000);
			},

			fetchOrders(pageNum, replaceList = true) {
				return this.$api.shifu.master_Order({
					coachId: this.shifuId,
					payType: this.currentIndex === 8 ? 7 : this.currentIndex,
					pageNum: pageNum,
					pageSize: this.limit
				}).then(res => {
					if (res.code === '-1' || !res.data || !res.data.list) {
						uni.showToast({
							icon: 'none',
							title: res.msg || '没有更多数据'
						});
						this.status = 'nomore';
					} else {
						const list = Array.isArray(res.data.list) ? res.data.list : [];
						const normalizedList = list
							.filter(item => this.currentIndex !== 8 || item.isAftermarket === 1)
							.map(item => ({
								...item,
								payType: parseInt(item.payType),
								// 确保 orderDiffPriceList 存在
								orderDiffPriceList: Array.isArray(item.orderDiffPriceList) ? item.orderDiffPriceList : []
							}));

						if (replaceList) {
							this.orderList = normalizedList;
						} else {
							this.orderList = [...this.orderList, ...normalizedList];
						}

						this.page = pageNum;
						this.status = list.length < this.limit ? 'nomore' : 'loadmore';
					}
					this.isLoading = false;
					return res;
				}).catch(err => {
					this.status = 'nomore';
					this.isLoading = false;
					uni.showToast({
						title: '加载失败',
						icon: 'none'
					});
					console.error('Error loading data:', err);
					return Promise.reject(err);
				});
			},

			goDetail(item) {
				uni.setStorageSync('orderdetails', item);
				uni.navigateTo({
					url: `/shifu/master_order_my?id=${item.id}`
				});
			},

			goUrl(e) {
				uni.navigateTo({
					url: e
				});
			},

			showConfirmModal(item, action) {
				uni.showModal({
					title: action === 'queren' ? '确认到达' : '开始服务',
					content: '请确认操作：' + (action === 'queren' ? '确认到达' : '开始服务'),
					confirmText: '确定',
					cancelText: '取消',
					success: (res) => {
						if (res.confirm) {
							if (action === 'queren') {
								this.queren(item);
							} else if (action === 'startFu') {
								this.startFu(item);
							}
						}
					}
				});
			},

			async startFu(item) {
				try {
					const res = await this.$api.shifu.shifuqueren({
						id: item.id,
						payType: 6
					});
					if (res.code === "200") {
						uni.showToast({
							title: '操作成功',
							icon: 'success'
						});
						this.refreshList();
					} else {
						uni.showToast({
							title: res.msg || '操作失败',
							icon: 'none'
						});
					}
				} catch (err) {
					uni.showToast({
						title: '请求失败',
						icon: 'none'
					});
					console.error('Error in startFu:', err);
				}
			},

			async queren(item) {
				try {
					const res = await this.$api.shifu.shifuqueren({
						id: item.id,
						payType: 5
					});
					if (res.code === "200") {
						uni.showToast({
							title: '操作成功',
							icon: 'success'
						});
						this.refreshList();
					} else {
						uni.showToast({
							title: res.msg || '操作失败',
							icon: 'none'
						});
					}
				} catch (err) {
					uni.showToast({
						title: '请求失败',
						icon: 'none'
					});
					console.error('Error in queren:', err);
				}
			},

			updateHigh(options) {
				const shiInfo = uni.getStorageSync('shiInfo');
				if (!shiInfo) {
					console.log('No shiInfo, skipping updateHighlight');
					return;
				}
				let shiInfoid;
				try {
					shiInfoid = JSON.parse(shiInfo);
				} catch (e) {
					console.error('Error parsing shiInfo:', e);
					return;
				}
				this.$api.service.updataHighlight({
					userId: shiInfoid.id,
					role: 2,
					payType: options.tab
				}).then(res => {
					console.log(res);
				}).catch(err => {
					console.error('Error updating highlight:', err);
				});
			},

			getList() {
				if (this.isLoading) return;
				this.isLoading = true;
				this.status = 'loading';
				this.fetchOrders(1, true);
			},

			handleHeader(item) {
				if (this.currentIndex === item.value) return;
				this.currentIndex = item.value;
				this.page = 0;
				this.orderList = [];
				this.status = 'loadmore';
				this.getList();
				this.updateHigh({
					tab: item.value
				});
			}
		},
		onLoad(options) {
			let shiInfo = uni.getStorageSync('shiInfo') || '{}';
			try {
				this.shifuId = JSON.parse(shiInfo).id;
			} catch (e) {
				console.error('Error parsing shiInfo:', e);
				this.shifuId = '';
			}

			if (options.tab) {
				this.currentIndex = parseInt(options.tab);
			}
			this.updateHigh(options);
			this.getList();
		}
	};
</script>

<style scoped lang="scss">
	.page {
		background-color: #F8F8F8;
		height: 100vh;
		overflow: auto;
		padding-top: 100rpx;

		.header {
			position: fixed;
			top: 0;
			left: 0;
			z-index: 100;
			width: 750rpx;
			height: 100rpx;
			background: #FFFFFF;
			display: flex;
			justify-content: space-around;
			align-items: center;

			.header_item {
				max-width: 85rpx;
				font-size: 28rpx;
				font-weight: 400;
				color: #999999;
				display: flex;
				justify-content: center;
				flex-wrap: wrap;

				.blue {
					margin-top: 8rpx;
					width: 38rpx;
					height: 6rpx;
					background: #2E80FE;
					border-radius: 4rpx;
				}
			}
		}

		.main {
			padding: 20rpx 30rpx;
			min-height: calc(100vh - 100rpx);

			.main_item {
				width: 690rpx;
				background: #FFFFFF;
				border-radius: 24rpx;
				padding: 28rpx 36rpx;
				margin-bottom: 20rpx;
				box-sizing: border-box;

				.head {
					display: flex;
					justify-content: space-between;
					align-items: center;
					font-size: 24rpx;
					font-weight: 400;
					color: #999999;

					.no {
						max-width: 500rpx;
						overflow: hidden;
						white-space: nowrap;
						text-overflow: ellipsis;
					}
				}

				.mid {
					margin-top: 20rpx;
					display: flex;
					justify-content: space-between;
					align-items: center;

					.lef {
						display: flex;
						align-items: center;

						image {
							width: 120rpx;
							height: 120rpx;
							flex-shrink: 0;
						}

						text {
							font-size: 28rpx;
							font-weight: 400;
							color: #333333;
							margin-left: 30rpx;
							max-width: 350rpx;
							overflow: hidden;
							white-space: nowrap;
							text-overflow: ellipsis;
						}
					}

					.righ {
						font-size: 28rpx;
						font-weight: 400;
						color: #333333;
						text-align: right;
						flex-shrink: 0;
					}
				}

				.bot {
					margin-top: 20rpx;
					font-size: 24rpx;
					font-weight: 400;
					color: #999999;
					display: flex;
					justify-content: flex-end;
					align-items: center;
					gap: 20rpx;

					.qzf {
						width: 148rpx;
						height: 48rpx;
						background: #2E80FE;
						border-radius: 50rpx;
						font-size: 20rpx;
						font-weight: 500;
						line-height: 48rpx;
						text-align: center;
						color: #fff;
						flex-shrink: 0;
					}
				}

				// 子订单样式
				.sub_orders {
					margin-top: 30rpx;
					padding-top: 20rpx;
					border-top: 1px solid #f0f0f0;

					.sub_title {
						font-size: 26rpx;
						font-weight: 500;
						color: #666;
						margin-bottom: 20rpx;
					}

					.sub_item {
						background: #f8f9fa;
						border-radius: 12rpx;
						padding: 20rpx;
						margin-bottom: 15rpx;

						.sub_head {
							display: flex;
							justify-content: space-between;
							align-items: center;
							margin-bottom: 15rpx;

							.sub_no {
								font-size: 22rpx;
								color: #666;
								max-width: 400rpx;
								overflow: hidden;
								white-space: nowrap;
								text-overflow: ellipsis;
							}

							.sub_status {
								font-size: 22rpx;
								color: #2E80FE;
								font-weight: 500;
							}
						}

						.sub_content {
							display: flex;
							justify-content: space-between;
							align-items: flex-end;

							.sub_info {
								flex: 1;

								.sub_amount {
									font-size: 24rpx;
									color: #333;
									font-weight: 500;
									margin-bottom: 8rpx;
								}

								.sub_reason {
									font-size: 22rpx;
									color: #666;
									margin-bottom: 8rpx;
									max-width: 300rpx;
									overflow: hidden;
									white-space: nowrap;
									text-overflow: ellipsis;
								}

								.sub_time {
									font-size: 20rpx;
									color: #999;
								}
							}

							.sub_actions {
								flex-shrink: 0;

								.sub_qzf {
									width: 120rpx;
									height: 40rpx;
									background: #ff6b6b;
									border-radius: 40rpx;
									font-size: 18rpx;
									font-weight: 400;
									line-height: 40rpx;
									text-align: center;
									color: #fff;
								}
							}
						}
					}
				}
			}
		}
	}

	.slot-content {
		padding: 30rpx;
	}

	.upload-container {
		margin-top: 16rpx;
		padding: 20rpx;
		border: 1rpx dashed #ccc;
		border-radius: 8rpx;
		background: #fafafa;
	}

	.parts-img {
		width: 120rpx;
		height: 120rpx;
		margin-right: 12rpx;
		margin-bottom: 12rpx;
		border-radius: 8rpx;
	}

	/* 简洁的差价申请弹窗样式 */
	.diff-apply-modal {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: rgba(0, 0, 0, 0.5);
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 9999;
	}

	.modal-content {
		width: 90%;
		max-width: 600rpx;
		background: #fff;
		border-radius: 16rpx;
		box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
		overflow: hidden;
	}

	.modal-header {
		position: relative;
		padding: 40rpx 50rpx 20rpx;
		border-bottom: 1rpx solid #f0f0f0;
	}

	.modal-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #333;
		text-align: center;
	}

	.close-btn {
		position: absolute;
		top: 20rpx;
		right: 20rpx;
		width: 48rpx;
		height: 48rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		border-radius: 50%;
		background: #f5f5f5;
		transition: background 0.2s;
	}

	.close-btn:active {
		background: #e0e0e0;
	}

	.close-icon {
		font-size: 28rpx;
		color: #666;
		line-height: 1;
	}

	.modal-body {
		padding: 40rpx;
	}

	.modal-footer {
		padding: 20rpx 40rpx 40rpx;
		display: flex;
		gap: 20rpx;
	}

	.btn-cancel, .btn-confirm {
		flex: 1;
		height: 80rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		border-radius: 8rpx;
		font-size: 28rpx;
		transition: all 0.2s;
	}

	.btn-cancel {
		background: #f8f8f8;
		color: #666;
		border: 1rpx solid #ddd;
	}

	.btn-cancel:active {
		background: #e8e8e8;
	}

	.btn-confirm {
		background: #007aff;
		color: #fff;
		border: 1rpx solid #007aff;
	}

	.btn-confirm:active {
		background: #0056cc;
	}

	/* 简洁表单样式 */
	.modal-body /deep/ .u-form-item {
		margin-bottom: 24rpx;
		padding-bottom: 20rpx;
		border-bottom: 1rpx solid #f0f0f0;
	}

	.modal-body /deep/ .u-form-item:last-child {
		border-bottom: none;
		margin-bottom: 0;
	}

	.modal-body /deep/ .u-form-item__label {
		font-size: 28rpx;
		color: #333;
		margin-bottom: 12rpx;
		font-weight: 500;
	}

	.modal-body /deep/ .u--input__content {
		background: #f8f8f8;
		border: 1rpx solid #e0e0e0;
		border-radius: 8rpx;
		padding: 16rpx;
		font-size: 28rpx;
		color: #333;
	}

	.modal-body /deep/ .u--textarea__content {
		background: #f8f8f8;
		border: 1rpx solid #e0e0e0;
		border-radius: 8rpx;
		padding: 16rpx;
		font-size: 28rpx;
		color: #333;
		min-height: 120rpx;
	}

	/* 原因类型显示样式 */
	.reason-type-display {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 16rpx;
		background: #f8f8f8;
		border: 1rpx solid #e0e0e0;
		border-radius: 8rpx;
	}

	.reason-type-text {
		font-size: 28rpx;
		color: #333;
	}

	.reason-type-badge {
		background: #007aff;
		color: #fff;
		font-size: 20rpx;
		padding: 4rpx 12rpx;
		border-radius: 12rpx;
	}
</style>