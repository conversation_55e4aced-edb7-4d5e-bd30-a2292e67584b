<template>
	<view class="login-container">
		<!-- Header -->
		<view class="header">
			<view class="back-btn" @click="goBack">
				<u-icon name="arrow-left" color="#333" size="24"></u-icon>
			</view>
			<view class="title">{{ currentMode === 'login' ? '登录' : currentMode === 'register' ? '注册' : '忘记密码' }}</view>
		</view>

		<!-- Logo Section -->
		<view class="logo-section">
			<image class="logo" src="/static/images/logo-index.jpg" mode="aspectFit"></image>
			<view class="app-name">今师傅</view>
			<view class="welcome-text">{{ getWelcomeText() }}</view>
		</view>

		<!-- Login Form -->
		<view class="form-container" v-if="currentMode === 'login'">
			<!-- Tab Switcher -->
			<view class="tab-switcher">
				<view class="tab-item" :class="{ active: loginType === 'password' }" @click="switchLoginType('password')">
					密码登录
				</view>
				<view class="tab-item" :class="{ active: loginType === 'sms' }" @click="switchLoginType('sms')">
					验证码登录
				</view>
			</view>

			<!-- Password Login -->
			<view v-if="loginType === 'password'">
				<view class="input-group">
					<view class="input-item">
						<u-icon name="phone" color="#999" size="20"></u-icon>
						<input class="input-field" type="number" placeholder="请输入手机号" v-model="loginForm.phone" maxlength="11" />
					</view>
					<view class="input-item">
						<u-icon name="lock" color="#999" size="20"></u-icon>
						<input class="input-field" :type="showPassword ? 'text' : 'password'" placeholder="请输入密码" v-model="loginForm.password" />
						<view class="eye-icon" @click="togglePassword">
							<u-icon :name="showPassword ? 'eye' : 'eye-off'" color="#999" size="20"></u-icon>
						</view>
					</view>
				</view>
				<view class="forgot-password" @click="switchMode('forgot')">忘记密码？</view>
			</view>

			<!-- SMS Login -->
			<view v-if="loginType === 'sms'">
				<view class="input-group">
					<view class="input-item">
						<u-icon name="phone" color="#999" size="20"></u-icon>
						<input class="input-field" type="number" placeholder="请输入手机号" v-model="smsForm.phone" maxlength="11" />
					</view>
					<view class="input-item">
						<u-icon name="checkmark-circle" color="#999" size="20"></u-icon>
						<input class="input-field" type="number" placeholder="请输入验证码" v-model="smsForm.code" maxlength="6" />
						<view class="sms-btn" @click="sendSmsCode" :class="{ disabled: smsCountdown > 0 }">
							{{ smsCountdown > 0 ? `${smsCountdown}s` : '获取验证码' }}
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- Register Form -->
		<view class="form-container" v-if="currentMode === 'register'">
			<view class="input-group">
				<view class="input-item">
					<u-icon name="phone" color="#999" size="20"></u-icon>
					<input class="input-field" type="number" placeholder="请输入手机号" v-model="registerForm.phone" maxlength="11" />
				</view>
				<view class="input-item">
					<u-icon name="lock" color="#999" size="20"></u-icon>
					<input class="input-field" :type="showPassword ? 'text' : 'password'" placeholder="请设置密码" v-model="registerForm.password" />
					<view class="eye-icon" @click="togglePassword">
						<u-icon :name="showPassword ? 'eye' : 'eye-off'" color="#999" size="20"></u-icon>
					</view>
				</view>
				<view class="input-item">
					<u-icon name="checkmark-circle" color="#999" size="20"></u-icon>
					<input class="input-field" type="number" placeholder="请输入验证码" v-model="registerForm.shortCode" maxlength="6" />
					<view class="sms-btn" @click="sendSmsCode" :class="{ disabled: smsCountdown > 0 }">
						{{ smsCountdown > 0 ? `${smsCountdown}s` : '获取验证码' }}
					</view>
				</view>
				<view class="input-item">
					<u-icon name="gift" color="#999" size="20"></u-icon>
					<input class="input-field" type="text" placeholder="邀请码（选填）" v-model="registerForm.pidInviteCode" />
				</view>
			</view>
		</view>

		<!-- Forgot Password Form -->
		<view class="form-container" v-if="currentMode === 'forgot'">
			<view class="input-group">
				<view class="input-item">
					<u-icon name="phone" color="#999" size="20"></u-icon>
					<input class="input-field" type="number" placeholder="请输入手机号" v-model="forgotForm.phone" maxlength="11" />
				</view>
				<view class="input-item">
					<u-icon name="lock" color="#999" size="20"></u-icon>
					<input class="input-field" :type="showPassword ? 'text' : 'password'" placeholder="请输入新密码" v-model="forgotForm.newPassword" />
					<view class="eye-icon" @click="togglePassword">
						<u-icon :name="showPassword ? 'eye' : 'eye-off'" color="#999" size="20"></u-icon>
					</view>
				</view>
				<view class="input-item">
					<u-icon name="lock" color="#999" size="20"></u-icon>
					<input class="input-field" :type="showConfirmPassword ? 'text' : 'password'" placeholder="请确认新密码" v-model="forgotForm.confirmPassword" />
					<view class="eye-icon" @click="toggleConfirmPassword">
						<u-icon :name="showConfirmPassword ? 'eye' : 'eye-off'" color="#999" size="20"></u-icon>
					</view>
				</view>
				<view class="input-item">
					<u-icon name="checkmark-circle" color="#999" size="20"></u-icon>
					<input class="input-field" type="number" placeholder="请输入验证码" v-model="forgotForm.shortCode" maxlength="6" />
					<view class="sms-btn" @click="sendSmsCode" :class="{ disabled: smsCountdown > 0 }">
						{{ smsCountdown > 0 ? `${smsCountdown}s` : '获取验证码' }}
					</view>
				</view>
			</view>
		</view>

		<!-- Agreement Section -->
		<view class="agreement-section">
			<view class="checkbox-container" @click="toggleAgreement">
				<view class="checkbox" :class="{ checked: agreedToTerms }">
					<u-icon v-if="agreedToTerms" name="checkmark" color="#fff" size="14"></u-icon>
				</view>
				<view class="agreement-text">
					我已阅读并同意
					<text class="link" @click.stop="navigateToAgreement('service')">《今师傅服务协议》</text>
					和
					<text class="link" @click.stop="navigateToAgreement('privacy')">《隐私政策》</text>
				</view>
			</view>
		</view>

		<!-- Action Button -->
		<view class="action-button" :class="{ disabled: !canSubmit }" @click="handleSubmit">
			{{ isLoading ? '处理中...' : getButtonText() }}
		</view>

		<!-- Switch Mode Links -->
		<view class="switch-links">
			<view v-if="currentMode === 'login'" class="link-text" @click="switchMode('register')">
				还没有账号？立即注册
			</view>
			<view v-if="currentMode === 'register'" class="link-text" @click="switchMode('login')">
				已有账号？立即登录
			</view>
			<view v-if="currentMode === 'forgot'" class="link-text" @click="switchMode('login')">
				返回登录
			</view>
		</view>
	</view>
</template>

<script>
	import { mapMutations } from 'vuex';
	import { md5 } from '@/utils/md5.js';

	export default {
		data() {
			return {
				currentMode: 'login', // login, register, forgot
				loginType: 'password', // password, sms
				showPassword: false,
				showConfirmPassword: false,
				agreedToTerms: false,
				isLoading: false,
				smsCountdown: 0,
				smsTimer: null,
				
				// Login forms
				loginForm: {
					phone: '17856179093',
					password: 'wudong123'
				},
				smsForm: {
					phone: '',
					code: ''
				},
				registerForm: {
					phone: '',
					password: '',
					shortCode: '',
					pidInviteCode: ''
				},
				forgotForm: {
					phone: '',
					newPassword: '',
					confirmPassword: '',
					shortCode: ''
				}
			};
		},
		computed: {
			canSubmit() {
				if (!this.agreedToTerms) return false;
				
				if (this.currentMode === 'login') {
					if (this.loginType === 'password') {
						return this.loginForm.phone && this.loginForm.password;
					} else {
						return this.smsForm.phone && this.smsForm.code;
					}
				} else if (this.currentMode === 'register') {
					return this.registerForm.phone && this.registerForm.password && this.registerForm.shortCode;
				} else if (this.currentMode === 'forgot') {
					return this.forgotForm.phone && this.forgotForm.newPassword && 
						   this.forgotForm.confirmPassword && this.forgotForm.shortCode;
				}
				return false;
			}
		},
		onLoad(options) {
			// 获取邀请码
			if (options.inviteCode) {
				this.registerForm.pidInviteCode = options.inviteCode;
			}
		},
		methods: {
			...mapMutations(['updateUserItem']),

			goBack() {
				uni.navigateBack();
			},

			getWelcomeText() {
				switch (this.currentMode) {
					case 'login': return '欢迎回来，请登录您的账号';
					case 'register': return '创建新账号，开始您的服务之旅';
					case 'forgot': return '重置密码，找回您的账号';
					default: return '';
				}
			},

			getButtonText() {
				switch (this.currentMode) {
					case 'login': return '登录';
					case 'register': return '注册';
					case 'forgot': return '重置密码';
					default: return '';
				}
			},

			switchMode(mode) {
				this.currentMode = mode;
				this.clearForms();
			},

			switchLoginType(type) {
				this.loginType = type;
			},

			togglePassword() {
				this.showPassword = !this.showPassword;
			},

			toggleConfirmPassword() {
				this.showConfirmPassword = !this.showConfirmPassword;
			},

			toggleAgreement() {
				this.agreedToTerms = !this.agreedToTerms;
			},

			clearForms() {
				this.loginForm = { phone: '', password: '' };
				this.smsForm = { phone: '', code: '' };
				this.registerForm = { phone: '', password: '', shortCode: '', pidInviteCode: '' };
				this.forgotForm = { phone: '', newPassword: '', confirmPassword: '', shortCode: '' };
			},

			navigateToAgreement(type) {
				let url = '../user/configuser';
				if (type === 'service') {
					url += '?type=service';
				} else if (type === 'privacy') {
					url += '?type=privacy';
				}
				uni.navigateTo({ url });
			},



			// 验证手机号
			validatePhone(phone) {
				const phoneReg = /^1[3-9]\d{9}$/;
				return phoneReg.test(phone);
			},

			// 发送短信验证码
			async sendSmsCode() {
				if (this.smsCountdown > 0) return;

				let phone = '';
				if (this.currentMode === 'login' && this.loginType === 'sms') {
					phone = this.smsForm.phone;
				} else if (this.currentMode === 'register') {
					phone = this.registerForm.phone;
				} else if (this.currentMode === 'forgot') {
					phone = this.forgotForm.phone;
				}

				if (!this.validatePhone(phone)) {
					return this.showToast('请输入正确的手机号');
				}

				try {
					// 调用发送验证码接口
					const response = await this.$api.base.sendSmsCode({ phone });

					if (response.code === '200') {
						this.showToast('验证码发送成功', 'success');
						this.startCountdown();
					} else {
						this.showToast(response.msg || '验证码发送失败，请重试');
					}
				} catch (error) {
					console.error('发送验证码失败:', error);
					this.showToast('验证码发送失败，请重试');
				}
			},

			// 开始倒计时
			startCountdown() {
				this.smsCountdown = 60;
				this.smsTimer = setInterval(() => {
					this.smsCountdown--;
					if (this.smsCountdown <= 0) {
						clearInterval(this.smsTimer);
						this.smsTimer = null;
					}
				}, 1000);
			},

			// 主要提交处理
			async handleSubmit() {
				if (!this.canSubmit || this.isLoading) return;

				this.isLoading = true;

				try {
					if (this.currentMode === 'login') {
						if (this.loginType === 'password') {
							await this.handlePasswordLogin();
						} else {
							await this.handleSmsLogin();
						}
					} else if (this.currentMode === 'register') {
						await this.handleRegister();
					} else if (this.currentMode === 'forgot') {
						await this.handleForgotPassword();
					}
				} catch (error) {
					console.error('操作失败:', error);
					this.showToast(error.message || '操作失败，请重试');
				} finally {
					this.isLoading = false;
				}
			},

			// 账号密码登录
			async handlePasswordLogin() {
				const { phone, password } = this.loginForm;

				if (!this.validatePhone(phone)) {
					throw new Error('请输入正确的手机号');
				}

				if (!password) {
					throw new Error('请输入密码');
				}

				const params = {
					phone,
					password: md5(password),
					platform: 2, // 用户端
					registrationId: '' // 极光推送id，暂时为空
				};

				// 使用API方法
				const response = await this.$api.base.appLoginByPass(params);
				await this.handleLoginSuccess(response);
			},

			// 短信验证码登录
			async handleSmsLogin() {
				const { phone, code } = this.smsForm;

				if (!this.validatePhone(phone)) {
					throw new Error('请输入正确的手机号');
				}

				if (!code) {
					throw new Error('请输入验证码');
				}

				const params = {
					phone,
					code,
					platform: 2, // 用户端
					registrationId: '' // 极光推送id，暂时为空
				};

				// 使用API方法
				const response = await this.$api.base.appLoginByCode(params);
				await this.handleLoginSuccess(response);
			},

			// 注册
			async handleRegister() {
				const { phone, password, shortCode, pidInviteCode } = this.registerForm;

				if (!this.validatePhone(phone)) {
					throw new Error('请输入正确的手机号');
				}

				if (!password) {
					throw new Error('请输入密码');
				}

				if (password.length < 6) {
					throw new Error('密码长度不能少于6位');
				}

				if (!shortCode) {
					throw new Error('请输入验证码');
				}

				const params = {
					phone,
					password: md5(password),
					shortCode,
					pidInviteCode: pidInviteCode || ''
				};

				// 使用API方法
				const response = await this.$api.base.appRegister(params);

				if (response.code === '200') {
					this.showToast('注册成功', 'success');
				} else {
					throw new Error(response.msg || '注册失败');
				}

				// 注册成功后自动登录
				setTimeout(() => {
					this.loginForm.phone = phone;
					this.loginForm.password = password;
					this.currentMode = 'login';
					this.loginType = 'password';
				}, 1500);
			},

			// 忘记密码
			async handleForgotPassword() {
				const { phone, newPassword, confirmPassword, shortCode } = this.forgotForm;

				if (!this.validatePhone(phone)) {
					throw new Error('请输入正确的手机号');
				}

				if (!newPassword) {
					throw new Error('请输入新密码');
				}

				if (newPassword.length < 6) {
					throw new Error('密码长度不能少于6位');
				}

				if (newPassword !== confirmPassword) {
					throw new Error('两次输入的密码不一致');
				}

				if (!shortCode) {
					throw new Error('请输入验证码');
				}

				const params = {
					phone,
					newPassword: md5(newPassword),
					confirmPassword: md5(confirmPassword),
					shortCode
				};

				// 使用API方法
				const response = await this.$api.base.appForgetPwd(params);

				if (response.code === '200') {
					this.showToast('密码重置成功', 'success');
				} else {
					throw new Error(response.msg || '密码重置失败');
				}

				// 重置成功后跳转到登录
				setTimeout(() => {
					this.loginForm.phone = phone;
					this.loginForm.password = '';
					this.currentMode = 'login';
					this.loginType = 'password';
				}, 1500);
			},

			// 处理登录成功
			async handleLoginSuccess(response) {
				console.log('登录响应:', response);

				if (!response || response.code !== '200') {
					throw new Error(response?.msg || '登录失败');
				}

				// 从响应头中提取token
				let token = '';
				const headers = response.header || {};
				console.log('响应头:', headers);

				// 查找Set-Cookie字段（不区分大小写）
				let setCookieValue = '';
				for (const key in headers) {
					if (key.toLowerCase() === 'set-cookie') {
						setCookieValue = headers[key];
						console.log('找到Set-Cookie:', setCookieValue);
						break;
					}
				}

				if (setCookieValue) {
					console.log('开始解析Set-Cookie:', setCookieValue);
					// 解析Set-Cookie，提取autograph
					const cookies = setCookieValue.split(';');
					console.log('分割后的cookies:', cookies);

					for (let i = 0; i < cookies.length; i++) {
						const cookie = cookies[i].trim();
						console.log(`检查cookie[${i}]:`, cookie);

						if (cookie.includes('autograph')) {
							console.log('找到包含autograph的cookie:', cookie);
							const match = cookie.match(/autograph=([^;]+)/);
							if (match) {
								token = match[1];
								console.log('提取到token:', token);
								break;
							}
						}
					}
				}

				if (!token) {
					console.error('未找到token，Set-Cookie:', setCookieValue);
					throw new Error('登录失败，未获取到token');
				}

				// 保存token和用户信息
				uni.setStorageSync('token', token);
				this.updateUserItem({
					key: 'autograph',
					val: token
				});

				// 保存用户信息
				const userInfo = response.data;
				const userInfoFormatted = {
					phone: userInfo.phone || '',
					avatarUrl: userInfo.avatarUrl || '/static/mine/default_user.png',
					nickName: userInfo.nickName || '用户',
					userId: userInfo.id || '',
					createTime: userInfo.createTime || '',
					pid: userInfo.pid || '',
					inviteCode: userInfo.inviteCode || ''
				};

				this.updateUserItem({
					key: 'userInfo',
					val: userInfoFormatted
				});

				// 保存到本地存储
				this.saveUserInfoToStorage(userInfoFormatted);

				this.showToast('登录成功', 'success');

				// 跳转回上一页或首页
				setTimeout(() => {
					uni.navigateBack({
						fail: () => {
							uni.switchTab({
								url: '/pages/mine'
							});
						}
					});
				}, 1500);
			},

			// 保存用户信息到本地存储
			saveUserInfoToStorage(userInfo) {
				uni.setStorageSync('phone', userInfo.phone);
				uni.setStorageSync('avatarUrl', userInfo.avatarUrl);
				uni.setStorageSync('nickName', userInfo.nickName);
				uni.setStorageSync('userId', userInfo.userId);
				uni.setStorageSync('pid', userInfo.pid);
			},

			// 显示提示信息
			showToast(title, icon = 'none') {
				uni.showToast({
					title,
					icon,
					duration: 2000
				});
			}
		}
	};
</script>

<style lang="scss" scoped>
	.login-container {
		min-height: 100vh;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		padding: 0 40rpx;
	}

	.header {
		display: flex;
		align-items: center;
		padding: 60rpx 0 40rpx;
		position: relative;

		.back-btn {
			position: absolute;
			left: 0;
			width: 60rpx;
			height: 60rpx;
			background: rgba(255, 255, 255, 0.2);
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.title {
			flex: 1;
			text-align: center;
			font-size: 36rpx;
			font-weight: bold;
			color: #fff;
		}
	}

	.logo-section {
		text-align: center;
		margin-bottom: 80rpx;

		.logo {
			width: 120rpx;
			height: 120rpx;
			border-radius: 24rpx;
			margin-bottom: 20rpx;
		}

		.app-name {
			font-size: 48rpx;
			font-weight: bold;
			color: #fff;
			margin-bottom: 16rpx;
		}

		.welcome-text {
			font-size: 28rpx;
			color: rgba(255, 255, 255, 0.8);
		}
	}

	.form-container {
		background: #fff;
		border-radius: 24rpx;
		padding: 60rpx 40rpx;
		margin-bottom: 40rpx;
		box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
	}

	.tab-switcher {
		display: flex;
		background: #f5f5f5;
		border-radius: 12rpx;
		margin-bottom: 60rpx;
		padding: 8rpx;

		.tab-item {
			flex: 1;
			text-align: center;
			padding: 20rpx;
			border-radius: 8rpx;
			font-size: 28rpx;
			color: #666;
			transition: all 0.3s;

			&.active {
				background: #fff;
				color: #667eea;
				font-weight: bold;
				box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.2);
			}
		}
	}

	.input-group {
		.input-item {
			display: flex;
			align-items: center;
			background: #f8f9fa;
			border-radius: 16rpx;
			padding: 0 30rpx;
			margin-bottom: 30rpx;
			height: 100rpx;

			.input-field {
				flex: 1;
				margin-left: 20rpx;
				font-size: 30rpx;
				color: #333;
			}

			.eye-icon, .sms-btn {
				margin-left: 20rpx;
			}

			.sms-btn {
				background: #667eea;
				color: #fff;
				padding: 16rpx 24rpx;
				border-radius: 8rpx;
				font-size: 24rpx;

				&.disabled {
					background: #ccc;
				}
			}
		}
	}

	.forgot-password {
		text-align: right;
		color: #667eea;
		font-size: 26rpx;
		margin-top: 20rpx;
	}

	.agreement-section {
		margin-bottom: 60rpx;

		.checkbox-container {
			display: flex;
			align-items: flex-start;

			.checkbox {
				width: 36rpx;
				height: 36rpx;
				border: 2rpx solid rgba(255, 255, 255, 0.5);
				border-radius: 6rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-right: 20rpx;
				margin-top: 4rpx;
				flex-shrink: 0;

				&.checked {
					background: #667eea;
					border-color: #667eea;
				}
			}

			.agreement-text {
				font-size: 26rpx;
				color: rgba(255, 255, 255, 0.8);
				line-height: 1.6;

				.link {
					color: #fff;
					text-decoration: underline;
				}
			}
		}
	}

	.action-button {
		width: 100%;
		height: 100rpx;
		background: linear-gradient(135deg, #667eea, #764ba2);
		border-radius: 50rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #fff;
		font-size: 36rpx;
		font-weight: bold;
		margin-bottom: 40rpx;
		box-shadow: 0 12rpx 40rpx rgba(102, 126, 234, 0.4);

		&.disabled {
			background: #ccc;
			box-shadow: none;
		}
	}

	.switch-links {
		text-align: center;

		.link-text {
			color: rgba(255, 255, 255, 0.8);
			font-size: 28rpx;
		}
	}
</style>
