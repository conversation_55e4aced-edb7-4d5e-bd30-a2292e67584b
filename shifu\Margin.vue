<template>
	<view class="page">
		<view class="box">
			<view class="money">{{money}}</view>
			<view class="title">保证金金额（元）</view>
			<!-- <view class="btn" @click="submit">缴纳保证金</view> -->
			<view class="btn" @click="submit">缴纳保证金</view>
			<view  v-if="money >0" class="btn" @click="tuikuan">退还保证金</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				money: '',
				cashPledge:'',
			}
		},
		methods: {
			tuikuan(){
				this.$api.shifu.tuikuanBzj().then(res=>{
					if(res.code==='-1'){
							uni.showToast({
								icon: 'none',
								title: res.msg
							})
						}else{
							uni.showToast({
								icon: 'success',
								title: res.data
							})
								this.getMoney()
						}
				})
			},
			getMoney() {
				this.$api.shifu.seeBzj().then(res => {
					if(res===-1){
							this.money = 0
						}
						this.money = res.data
					
				})
			},
			getprice() {
				this. $api.base.getConfig().then(res => {
					if(res.code==='-1'){
							this.money = 0
						}
						console.log(res)
					this.cashPledge=res.data.cashPledge
				})
			},
			submit() {
				if (this.money == 0 || this.money == '') {
					this.confirmPay()
				} else {
					uni.showToast({
						icon: 'none',
						title: '您已缴纳保证金快去接单吧'
					})
				}
			},
			confirmPay() {
				this.$api.shifu.nowPay({
					payPrice :this.cashPledge,
					couponId: 0,
					type:1,
				}).then(res => {
					if(res.code==='-1'){
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
					}else{
						console.log(res)
						let obj = res.data
						let packageStr = "prepay_id=" + obj.prepayId;
						console.log(String(packageStr))
						console.log(obj.nonceStr)
						console.log(packageStr)
						console.log(obj.nonceStr)
						console.log(String(obj.timestamp))
						console.log(obj.sign)
						
						
						
						
						
						const paymentParams = {
							timeStamp: String(obj.timestamp), // 一定要是 string 
							nonceStr: obj.nonceStr,
							package: "prepay_id=" + obj.prepayId,
							signType: 'MD5',
							paySign: obj.sign
						};
						console.log(JSON.stringify(paymentParams));
						
						uni.requestPayment({
							"provider": 'wxpay',
						
							timeStamp: String(obj.timestamp),
							nonceStr: obj.nonceStr,
							package: "prepay_id=" + obj.prepayId,
							partnerid: obj.partnerId,
						
							signType: "MD5",
							paySign: obj.sign,
							appId: obj.appId,
							// total_fee:"10",
							success: (res1) => {
								// 支付成功回调
								console.log('支付成功', res1);
								uni.showToast({
									title: '支付成功',
									icon: 'success'
								})
								setTimeout(() => {
									uni.redirectTo({
										url: '/shifu/Margin'
									})
								}, 1000)
							},
							fail: (err) => {
								// 1) 直接打印对象（在控制台里能展开查看）
								console.error('requestPayment fail object:', err);
						
								// 2) 或者打印序列化后的 JSON 字符串
								console.error('requestPayment fail JSON:', JSON.stringify(err));
						
								// 根据 err.errMsg 做判断
								if (err.errMsg.includes('fail cancel')) {
									uni.showToast({
										title: '您已取消支付',
										icon: 'none'
									});
								} else {
									uni.showToast({
										title: '支付失败，请稍后重试',
										icon: 'none'
									});
								}
								
								// 支付失败回调
								console.error('支付失败', err);
								uni.showToast({
									title: '支付失败请检查网络',
									icon: 'error'
								})
						
							},
						})
					}
				})
			}
		},
		onLoad() {
			this.getMoney()
			this.getprice()
		}
	}
</script>

<style scoped lang="scss">
	.page {
		background: #F8F8F8;
		height: 100vh;

		.box {
			padding: 50rpx 82rpx;
			background: #fff;

			.money {
				margin: 0 auto;
				width: fit-content;
				font-size: 80rpx;
				font-weight: 500;
				color: #171717;
			}

			.title {
				margin: 0 auto;
				margin-top: 20rpx;
				width: fit-content;
				font-size: 24rpx;
				font-weight: 400;
				color: #171717;
			}

			.btn {
				margin: 0 auto;
				margin-top: 64rpx;
				width: 584rpx;
				height: 98rpx;
				background: #2E80FE;
				border-radius: 12rpx 12rpx 12rpx 12rpx;
				line-height: 98rpx;
				text-align: center;
				font-size: 32rpx;
				font-weight: 500;
				color: #FFFFFF;
			}
		}
	}
</style>